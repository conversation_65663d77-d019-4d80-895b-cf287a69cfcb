#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
温度序列曲线图绘制脚本
绘制高级温度序列优化结果的可视化图表
"""

import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 设置为非交互模式
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

print("导入模块完成")

def load_and_plot_temperature_data(csv_file_path):
    """
    加载CSV数据并绘制温度曲线图
    
    Args:
        csv_file_path (str): CSV文件路径
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file_path, encoding='utf-8')
        print(f"成功加载数据，共 {len(df)} 个数据点")
        print(f"数据列: {list(df.columns)}")
        
        # 创建图形和子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('高级温度序列优化结果分析', fontsize=16, fontweight='bold')
        
        # 1. 温度随时间变化曲线
        ax1 = axes[0, 0]
        ax1.plot(df['时间(分钟)'], df['温度(°C)'], 'b-', linewidth=1.5, alpha=0.8)
        ax1.set_xlabel('时间 (分钟)')
        ax1.set_ylabel('温度 (°C)')
        ax1.set_title('温度随时间变化曲线')
        ax1.grid(True, alpha=0.3)
        
        # 添加温度范围信息
        temp_min = df['温度(°C)'].min()
        temp_max = df['温度(°C)'].max()
        temp_mean = df['温度(°C)'].mean()
        ax1.axhline(y=temp_mean, color='r', linestyle='--', alpha=0.7, label=f'平均温度: {temp_mean:.2f}°C')
        ax1.legend()
        
        # 2. 温度分布直方图
        ax2 = axes[0, 1]
        ax2.hist(df['温度(°C)'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.set_xlabel('温度 (°C)')
        ax2.set_ylabel('频次')
        ax2.set_title('温度分布直方图')
        ax2.axvline(x=temp_mean, color='r', linestyle='--', alpha=0.7, label=f'平均值: {temp_mean:.2f}°C')
        ax2.legend()
        
        # 3. 适应度随时间变化
        ax3 = axes[1, 0]
        ax3.plot(df['时间(分钟)'], df['适应度'], 'g-', linewidth=1.5, alpha=0.8)
        ax3.set_xlabel('时间 (分钟)')
        ax3.set_ylabel('适应度')
        ax3.set_title('适应度随时间变化')
        ax3.grid(True, alpha=0.3)
        
        # 4. 温度变化率分析
        ax4 = axes[1, 1]
        # 计算温度变化率
        temp_diff = np.diff(df['温度(°C)'])
        time_diff = np.diff(df['时间(分钟)'])
        temp_rate = temp_diff / time_diff
        
        ax4.plot(df['时间(分钟)'][1:], temp_rate, 'orange', linewidth=1, alpha=0.7)
        ax4.set_xlabel('时间 (分钟)')
        ax4.set_ylabel('温度变化率 (°C/分钟)')
        ax4.set_title('温度变化率')
        ax4.grid(True, alpha=0.3)
        ax4.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        output_path = csv_file_path.replace('.csv', '_analysis.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存至: {output_path}")
        
        # 显示统计信息
        print("\n=== 数据统计信息 ===")
        print(f"温度范围: {temp_min:.2f}°C - {temp_max:.2f}°C")
        print(f"平均温度: {temp_mean:.2f}°C")
        print(f"温度标准差: {df['温度(°C)'].std():.2f}°C")
        print(f"时间范围: {df['时间(分钟)'].min():.1f} - {df['时间(分钟)'].max():.1f} 分钟")
        print(f"平均适应度: {df['适应度'].mean():.6f}")
        print(f"迭代次数: {df['迭代次数'].iloc[0]}")
        print(f"是否收敛: {df['是否收敛'].iloc[0]}")
        
        # 不显示图表，只保存
        # plt.show()  # 注释掉显示，避免在服务器环境中出错

        return df
        
    except Exception as e:
        print(f"处理数据时出错: {e}")
        return None

def create_simple_temperature_plot(csv_file_path):
    """
    创建简单的温度曲线图
    
    Args:
        csv_file_path (str): CSV文件路径
    """
    try:
        # 读取数据
        df = pd.read_csv(csv_file_path, encoding='utf-8')
        
        # 创建简单的温度曲线图
        plt.figure(figsize=(12, 8))
        plt.plot(df['时间(分钟)'], df['温度(°C)'], 'b-', linewidth=2, alpha=0.8)
        
        plt.xlabel('时间 (分钟)', fontsize=12)
        plt.ylabel('温度 (°C)', fontsize=12)
        plt.title('高级温度序列优化 - 温度随时间变化', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        
        # 添加统计信息
        temp_min = df['温度(°C)'].min()
        temp_max = df['温度(°C)'].max()
        temp_mean = df['温度(°C)'].mean()
        
        plt.axhline(y=temp_mean, color='r', linestyle='--', alpha=0.7, 
                   label=f'平均温度: {temp_mean:.2f}°C')
        plt.legend()
        
        # 添加文本注释
        plt.text(0.02, 0.98, f'温度范围: {temp_min:.2f}°C - {temp_max:.2f}°C\n'
                              f'数据点数: {len(df)}', 
                 transform=plt.gca().transAxes, fontsize=10,
                 verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        # 保存图片
        output_path = csv_file_path.replace('.csv', '_simple_plot.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"简单温度曲线图已保存至: {output_path}")
        
        # plt.show()  # 注释掉显示，避免在服务器环境中出错
        
    except Exception as e:
        print(f"创建简单图表时出错: {e}")

if __name__ == "__main__":
    print("脚本开始执行...")

    # CSV文件路径
    csv_file = "results/advanced_temperature_sequence_20250727_142852.csv"

    # 检查文件是否存在
    print(f"检查文件: {csv_file}")
    if not Path(csv_file).exists():
        print(f"错误: 文件 {csv_file} 不存在")
        exit(1)

    print("文件存在，开始绘制温度序列曲线图...")

    # 绘制详细分析图表
    print("\n1. 绘制详细分析图表...")
    try:
        df = load_and_plot_temperature_data(csv_file)
        print("详细分析图表完成")
    except Exception as e:
        print(f"详细分析图表出错: {e}")

    # 绘制简单温度曲线图
    print("\n2. 绘制简单温度曲线图...")
    try:
        create_simple_temperature_plot(csv_file)
        print("简单温度曲线图完成")
    except Exception as e:
        print(f"简单温度曲线图出错: {e}")

    print("\n绘图完成!")
